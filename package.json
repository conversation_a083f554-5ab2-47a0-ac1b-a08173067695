{"name": "rjsf-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "6.0.2", "@mui/material": "6.0.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@rjsf/core": "6.0.0-beta.11", "@rjsf/shadcn": "6.0.0-beta.11", "@rjsf/utils": "6.0.0-beta.11", "@rjsf/validator-ajv8": "6.0.0-beta.11", "@tailwindcss/vite": "^4.1.10", "@types/lodash-es": "^4.17.12", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "uuid": "^11.1.0", "vitest": "^3.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}, "packageManager": "pnpm@8.8.0+sha1.9922e8b650d393700209ccd81e0ebdbcbe43b0d3"}