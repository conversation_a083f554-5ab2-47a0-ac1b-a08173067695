import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { FieldPalette } from '../field-palette';

// Mock DnD Kit
vi.mock('@dnd-kit/core', () => ({
  useDraggable: () => ({
    attributes: {},
    listeners: {},
    setNodeRef: vi.fn(),
    isDragging: false,
  }),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Type: ({ className }: { className?: string }) => <div className={className} data-testid="type-icon" />,
  TextQuote: ({ className }: { className?: string }) => <div className={className} data-testid="text-quote-icon" />,
  ToggleLeft: ({ className }: { className?: string }) => <div className={className} data-testid="toggle-left-icon" />,
  Hash: ({ className }: { className?: string }) => <div className={className} data-testid="hash-icon" />,
  Layers: ({ className }: { className?: string }) => <div className={className} data-testid="layers-icon" />,
  GitBranch: ({ className }: { className?: string }) => <div className={className} data-testid="git-branch-icon" />,
}));

describe('FieldPalette', () => {
  it('should render all field types', () => {
    render(<FieldPalette />);

    // Check that all field types are rendered
    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Number')).toBeInTheDocument();
    expect(screen.getByText('Yes/No')).toBeInTheDocument();
    expect(screen.getByText('Object')).toBeInTheDocument();
    expect(screen.getByText('List')).toBeInTheDocument();
    expect(screen.getByText('If-Then-Else')).toBeInTheDocument();
  });

  it('should render correct icons for each field type', () => {
    render(<FieldPalette />);

    expect(screen.getByTestId('text-quote-icon')).toBeInTheDocument();
    expect(screen.getByTestId('hash-icon')).toBeInTheDocument();
    expect(screen.getByTestId('toggle-left-icon')).toBeInTheDocument();
    expect(screen.getByTestId('type-icon')).toBeInTheDocument();
    expect(screen.getByTestId('layers-icon')).toBeInTheDocument();
    expect(screen.getByTestId('git-branch-icon')).toBeInTheDocument();
  });

  it('should render field types in correct order', () => {
    render(<FieldPalette />);

    const fieldTypes = screen.getAllByRole('generic').filter(el => 
      el.textContent && ['Text', 'Number', 'Yes/No', 'Object', 'List', 'If-Then-Else'].includes(el.textContent)
    );

    expect(fieldTypes[0]).toHaveTextContent('Text');
    expect(fieldTypes[1]).toHaveTextContent('Number');
    expect(fieldTypes[2]).toHaveTextContent('Yes/No');
    expect(fieldTypes[3]).toHaveTextContent('Object');
    expect(fieldTypes[4]).toHaveTextContent('List');
    expect(fieldTypes[5]).toHaveTextContent('If-Then-Else');
  });

  it('should have proper styling classes', () => {
    render(<FieldPalette />);

    const container = screen.getByText('Text').closest('.group');
    expect(container).toHaveClass('group', 'rounded-md', 'border', 'bg-background');
  });

  it('should render with draggable functionality', () => {
    const mockUseDraggable = vi.fn(() => ({
      attributes: { 'data-testid': 'draggable' },
      listeners: { onMouseDown: vi.fn() },
      setNodeRef: vi.fn(),
      isDragging: false,
    }));

    vi.mocked(require('@dnd-kit/core')).useDraggable = mockUseDraggable;

    render(<FieldPalette />);

    // Should call useDraggable for each field type
    expect(mockUseDraggable).toHaveBeenCalledTimes(6);

    // Check that useDraggable is called with correct data
    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'text-field',
      data: {
        type: 'string',
        label: 'Text',
      },
    });

    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'number-field',
      data: {
        type: 'number',
        label: 'Number',
      },
    });

    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'boolean-field',
      data: {
        type: 'boolean',
        label: 'Yes/No',
      },
    });

    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'object-field',
      data: {
        type: 'object',
        label: 'Object',
      },
    });

    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'array-field',
      data: {
        type: 'array',
        label: 'List',
      },
    });

    expect(mockUseDraggable).toHaveBeenCalledWith({
      id: 'if-block',
      data: {
        type: 'if_block',
        label: 'If-Then-Else',
      },
    });
  });

  it('should handle dragging state', () => {
    const mockUseDraggable = vi.fn(() => ({
      attributes: {},
      listeners: {},
      setNodeRef: vi.fn(),
      isDragging: true, // Simulate dragging state
    }));

    vi.mocked(require('@dnd-kit/core')).useDraggable = mockUseDraggable;

    render(<FieldPalette />);

    // When dragging, should apply dragging styles
    const draggableElements = screen.getAllByText(/Text|Number|Yes\/No|Object|List|If-Then-Else/);
    draggableElements.forEach(element => {
      const draggableContainer = element.closest('[class*="cursor-grab"]');
      expect(draggableContainer).toHaveClass('cursor-grabbing', 'opacity-50');
    });
  });

  it('should have correct accessibility attributes', () => {
    render(<FieldPalette />);

    // Each field type should be interactive
    const textField = screen.getByText('Text').closest('div');
    expect(textField).toHaveClass('cursor-grab');
  });

  it('should maintain consistent structure', () => {
    render(<FieldPalette />);

    // Check that each field type has the expected structure
    const fieldTypes = ['Text', 'Number', 'Yes/No', 'Object', 'List', 'If-Then-Else'];
    
    fieldTypes.forEach(fieldType => {
      const element = screen.getByText(fieldType);
      const container = element.closest('.group');
      const iconContainer = container?.querySelector('[data-testid*="icon"]');
      
      expect(container).toBeInTheDocument();
      expect(iconContainer).toBeInTheDocument();
      expect(element).toHaveClass('text-sm', 'font-medium');
    });
  });

  it('should handle hover states correctly', () => {
    render(<FieldPalette />);

    const textField = screen.getByText('Text').closest('.group');
    expect(textField).toHaveClass('hover:bg-accent', 'hover:text-accent-foreground');
  });
});
