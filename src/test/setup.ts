import '@testing-library/jest-dom';

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
    readText: vi.fn().mockResolvedValue(''),
  },
});

// Mock DragEvent
global.DragEvent = class DragEvent extends Event {
  constructor(type: string, eventInitDict?: DragEventInit) {
    super(type, eventInitDict);
  }
  dataTransfer = {
    dropEffect: 'none' as const,
    effectAllowed: 'all' as const,
    files: [] as File[],
    items: [] as DataTransferItem[],
    types: [] as string[],
    clearData: vi.fn(),
    getData: vi.fn().mockReturnValue(''),
    setData: vi.fn(),
    setDragImage: vi.fn(),
  };
};
