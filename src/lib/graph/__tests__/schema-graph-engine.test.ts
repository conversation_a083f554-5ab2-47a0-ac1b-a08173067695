import { describe, it, expect, beforeEach } from 'vitest';
import { SchemaGraphEngine, type SchemaGraph, type FieldNode } from '../schema-graph-engine';
import type { RJSFSchema } from '@rjsf/utils';

describe('SchemaGraphEngine', () => {
  let engine: SchemaGraphEngine;
  let baseGraph: SchemaGraph;

  beforeEach(() => {
    engine = new SchemaGraphEngine();
    baseGraph = {
      nodes: {
        root: {
          id: 'root',
          key: 'root',
          type: 'object',
          title: 'Root',
          children: [],
        },
      },
      rootId: 'root',
    };
  });

  describe('addNode', () => {
    it('should add a new node to the graph', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Test Field',
      };

      const result = engine.addNode(baseGraph, nodeData);

      expect(Object.keys(result.nodes)).toHaveLength(2);
      const newNodeId = Object.keys(result.nodes).find(id => id !== 'root');
      expect(newNodeId).toBeDefined();
      
      const newNode = result.nodes[newNodeId!];
      expect(newNode.title).toBe('Test Field');
      expect(newNode.type).toBe('string');
      expect(newNode.parentId).toBe('root');
      expect(result.nodes.root.children).toContain(newNodeId);
    });

    it('should generate unique keys for nodes with same title', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: '',
        type: 'string',
        title: 'Test Field',
      };

      const result1 = engine.addNode(baseGraph, nodeData);
      const result2 = engine.addNode(result1, nodeData);

      const nodeIds = Object.keys(result2.nodes).filter(id => id !== 'root');
      const keys = nodeIds.map(id => result2.nodes[id].key);
      
      expect(keys).toHaveLength(2);
      expect(keys[0]).toBe('testField');
      expect(keys[1]).toBe('testField2');
    });

    it('should add children array for object and array types', () => {
      const objectNode: Omit<FieldNode, 'id'> = {
        key: 'obj',
        type: 'object',
        title: 'Object Field',
      };

      const arrayNode: Omit<FieldNode, 'id'> = {
        key: 'arr',
        type: 'array',
        title: 'Array Field',
      };

      const result1 = engine.addNode(baseGraph, objectNode);
      const result2 = engine.addNode(result1, arrayNode);

      const objectNodeId = Object.keys(result2.nodes).find(id => 
        result2.nodes[id].type === 'object' && id !== 'root'
      );
      const arrayNodeId = Object.keys(result2.nodes).find(id => 
        result2.nodes[id].type === 'array'
      );

      expect(result2.nodes[objectNodeId!].children).toEqual([]);
      expect(result2.nodes[arrayNodeId!].children).toEqual([]);
    });
  });

  describe('removeNode', () => {
    it('should remove a node and its children', () => {
      // Add parent and child nodes
      const parentData: Omit<FieldNode, 'id'> = {
        key: 'parent',
        type: 'object',
        title: 'Parent',
      };
      
      const result1 = engine.addNode(baseGraph, parentData);
      const parentId = Object.keys(result1.nodes).find(id => id !== 'root')!;
      
      const childData: Omit<FieldNode, 'id'> = {
        key: 'child',
        type: 'string',
        title: 'Child',
      };
      
      const result2 = engine.addNode(result1, childData, parentId);
      expect(Object.keys(result2.nodes)).toHaveLength(3);

      // Remove parent node
      const result3 = engine.removeNode(result2, parentId);
      
      expect(Object.keys(result3.nodes)).toHaveLength(1);
      expect(result3.nodes.root.children).toHaveLength(0);
    });

    it('should not remove root node', () => {
      const result = engine.removeNode(baseGraph, 'root');
      expect(result.nodes.root).toBeDefined();
    });
  });

  describe('updateNode', () => {
    it('should update node properties', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Original Title',
      };

      const result1 = engine.addNode(baseGraph, nodeData);
      const nodeId = Object.keys(result1.nodes).find(id => id !== 'root')!;

      const result2 = engine.updateNode(result1, nodeId, {
        title: 'Updated Title',
        description: 'New description',
      });

      const updatedNode = result2.nodes[nodeId];
      expect(updatedNode.title).toBe('Updated Title');
      expect(updatedNode.description).toBe('New description');
      expect(updatedNode.type).toBe('string'); // Should preserve other properties
    });
  });

  describe('moveNode', () => {
    it('should move a node to a new parent', () => {
      // Create structure: root -> parent1, parent2 -> child
      const parent1Data: Omit<FieldNode, 'id'> = {
        key: 'parent1',
        type: 'object',
        title: 'Parent 1',
      };
      
      const parent2Data: Omit<FieldNode, 'id'> = {
        key: 'parent2',
        type: 'object',
        title: 'Parent 2',
      };

      const result1 = engine.addNode(baseGraph, parent1Data);
      const result2 = engine.addNode(result1, parent2Data);
      
      const parent1Id = Object.keys(result2.nodes).find(id => 
        result2.nodes[id].key === 'parent1'
      )!;
      const parent2Id = Object.keys(result2.nodes).find(id => 
        result2.nodes[id].key === 'parent2'
      )!;

      const childData: Omit<FieldNode, 'id'> = {
        key: 'child',
        type: 'string',
        title: 'Child',
      };

      const result3 = engine.addNode(result2, childData, parent1Id);
      const childId = Object.keys(result3.nodes).find(id => 
        result3.nodes[id].key === 'child'
      )!;

      // Move child from parent1 to parent2
      const result4 = engine.moveNode(result3, childId, parent2Id);

      expect(result4.nodes[parent1Id].children).not.toContain(childId);
      expect(result4.nodes[parent2Id].children).toContain(childId);
      expect(result4.nodes[childId].parentId).toBe(parent2Id);
    });
  });

  describe('reorderNode', () => {
    it('should reorder nodes within the same parent', () => {
      // Add multiple children to root
      const child1Data: Omit<FieldNode, 'id'> = { key: 'child1', type: 'string', title: 'Child 1' };
      const child2Data: Omit<FieldNode, 'id'> = { key: 'child2', type: 'string', title: 'Child 2' };
      const child3Data: Omit<FieldNode, 'id'> = { key: 'child3', type: 'string', title: 'Child 3' };

      const result1 = engine.addNode(baseGraph, child1Data);
      const result2 = engine.addNode(result1, child2Data);
      const result3 = engine.addNode(result2, child3Data);

      const child1Id = Object.keys(result3.nodes).find(id => result3.nodes[id].key === 'child1')!;
      const originalOrder = result3.nodes.root.children!;

      // Move first child to last position
      const result4 = engine.reorderNode(result3, child1Id, 2);
      const newOrder = result4.nodes.root.children!;

      expect(newOrder[2]).toBe(child1Id);
      expect(newOrder).toHaveLength(3);
      expect(newOrder).not.toEqual(originalOrder);
    });
  });

  describe('compileToJsonSchema', () => {
    it('should compile a simple graph to JSON schema', () => {
      const stringData: Omit<FieldNode, 'id'> = {
        key: 'name',
        type: 'string',
        title: 'Name',
        required: true,
      };

      const numberData: Omit<FieldNode, 'id'> = {
        key: 'age',
        type: 'number',
        title: 'Age',
        minimum: 0,
        maximum: 120,
      };

      const result1 = engine.addNode(baseGraph, stringData);
      const result2 = engine.addNode(result1, numberData);

      const schema = engine.compileToJsonSchema(result2);

      expect(schema.type).toBe('object');
      expect(schema.properties).toBeDefined();
      expect(schema.properties!.name).toEqual({
        type: 'string',
        title: 'Name',
      });
      expect(schema.properties!.age).toEqual({
        type: 'number',
        title: 'Age',
        minimum: 0,
        maximum: 120,
      });
      expect(schema.required).toEqual(['name']);
    });

    it('should handle nested objects', () => {
      const addressData: Omit<FieldNode, 'id'> = {
        key: 'address',
        type: 'object',
        title: 'Address',
      };

      const streetData: Omit<FieldNode, 'id'> = {
        key: 'street',
        type: 'string',
        title: 'Street',
      };

      const result1 = engine.addNode(baseGraph, addressData);
      const addressId = Object.keys(result1.nodes).find(id => id !== 'root')!;
      const result2 = engine.addNode(result1, streetData, addressId);

      const schema = engine.compileToJsonSchema(result2);

      expect(schema.properties!.address).toBeDefined();
      expect((schema.properties!.address as any).type).toBe('object');
      expect((schema.properties!.address as any).properties.street).toEqual({
        type: 'string',
        title: 'Street',
      });
    });
  });

  describe('fromJsonSchema', () => {
    it('should import a simple JSON schema', () => {
      const inputSchema: RJSFSchema = {
        type: 'object',
        title: 'Person',
        properties: {
          name: {
            type: 'string',
            title: 'Full Name',
          },
          age: {
            type: 'number',
            title: 'Age',
            minimum: 0,
          },
        },
        required: ['name'],
      };

      const graph = engine.fromJsonSchema(inputSchema);

      expect(graph.nodes.root.title).toBe('Person');
      expect(graph.nodes.root.children).toHaveLength(2);

      const nameNode = Object.values(graph.nodes).find(node => node.key === 'name');
      const ageNode = Object.values(graph.nodes).find(node => node.key === 'age');

      expect(nameNode).toBeDefined();
      expect(nameNode!.type).toBe('string');
      expect(nameNode!.title).toBe('Full Name');
      expect(nameNode!.required).toBe(true);

      expect(ageNode).toBeDefined();
      expect(ageNode!.type).toBe('number');
      expect(ageNode!.minimum).toBe(0);
    });
  });

  describe('validateGraph', () => {
    it('should validate a correct graph', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Test',
      };

      const result = engine.addNode(baseGraph, nodeData);
      const validation = engine.validateGraph(result);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect orphaned nodes', () => {
      // Manually create a graph with orphaned node
      const invalidGraph: SchemaGraph = {
        nodes: {
          root: {
            id: 'root',
            key: 'root',
            type: 'object',
            title: 'Root',
            children: [],
          },
          orphan: {
            id: 'orphan',
            key: 'orphan',
            type: 'string',
            title: 'Orphan',
            parentId: 'nonexistent',
          },
        },
        rootId: 'root',
      };

      const validation = engine.validateGraph(invalidGraph);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Orphaned node detected: orphan');
    });
  });
});
