import { describe, it, expect, beforeEach } from 'vitest';
import { granularMergeEngine } from '../granular-merge-engine';
import type { RJSFSchema } from '@rjsf/utils';

describe('GranularSchemaMergeEngine', () => {
  let originalSchema: RJSFSchema;
  let editedSchema: RJSFSchema;

  beforeEach(() => {
    originalSchema = {
      type: 'object',
      title: 'Test Schema',
      properties: {
        name: {
          type: 'string',
          title: 'Name',
        },
        age: {
          type: 'number',
          title: 'Age',
        },
      },
      required: ['name'],
      allOf: [
        {
          if: { properties: { age: { minimum: 18 } } },
          then: { properties: { canVote: { type: 'boolean', default: true } } },
        },
      ],
    };
  });

  describe('Basic Property Changes', () => {
    it('should preserve complex constructs when editing simple properties', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          name: {
            type: 'string',
            title: 'Full Name', // Changed title
          },
        },
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.allOf).toEqual(originalSchema.allOf);
      expect(result.mergedSchema.properties?.name?.title).toBe('Full Name');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].path).toBe('properties.name.title');
    });

    it('should handle property additions while preserving complex constructs', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          email: {
            type: 'string',
            format: 'email',
            title: 'Email',
          },
        },
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.allOf).toEqual(originalSchema.allOf);
      expect(result.mergedSchema.properties?.email).toEqual({
        type: 'string',
        format: 'email',
        title: 'Email',
      });
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('add');
    });

    it('should handle property removals while preserving complex constructs', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          name: originalSchema.properties?.name,
          // age property removed
        },
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.allOf).toEqual(originalSchema.allOf);
      expect(result.mergedSchema.properties?.age).toBeUndefined();
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('remove');
    });
  });

  describe('Complex Construct Preservation', () => {
    it('should preserve allOf constructs', () => {
      editedSchema = {
        ...originalSchema,
        title: 'Updated Test Schema', // Simple change
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.allOf).toEqual(originalSchema.allOf);
      expect(result.mergedSchema.title).toBe('Updated Test Schema');
    });

    it('should preserve oneOf constructs', () => {
      const schemaWithOneOf: RJSFSchema = {
        type: 'object',
        properties: {
          contact: {
            oneOf: [
              { properties: { email: { type: 'string' } } },
              { properties: { phone: { type: 'string' } } },
            ],
          },
        },
      };

      const editedWithOneOf = {
        ...schemaWithOneOf,
        title: 'Contact Schema',
      };

      const result = granularMergeEngine.mergeSchemas(schemaWithOneOf, editedWithOneOf);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.properties?.contact?.oneOf).toEqual(
        schemaWithOneOf.properties?.contact?.oneOf
      );
    });

    it('should preserve conditional schemas (if/then/else)', () => {
      const schemaWithConditional: RJSFSchema = {
        type: 'object',
        properties: {
          hasChildren: { type: 'boolean' },
          children: { type: 'array' },
        },
        if: { properties: { hasChildren: { const: true } } },
        then: { required: ['children'] },
        else: { properties: { children: { const: [] } } },
      };

      const editedConditional = {
        ...schemaWithConditional,
        title: 'Family Schema',
      };

      const result = granularMergeEngine.mergeSchemas(schemaWithConditional, editedConditional);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.if).toEqual(schemaWithConditional.if);
      expect(result.mergedSchema.then).toEqual(schemaWithConditional.then);
      expect(result.mergedSchema.else).toEqual(schemaWithConditional.else);
    });

    it('should preserve dependencies', () => {
      const schemaWithDeps: RJSFSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          creditCard: { type: 'string' },
          billingAddress: { type: 'string' },
        },
        dependencies: {
          creditCard: ['billingAddress'],
        },
      };

      const editedWithDeps = {
        ...schemaWithDeps,
        title: 'Payment Schema',
      };

      const result = granularMergeEngine.mergeSchemas(schemaWithDeps, editedWithDeps);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.dependencies).toEqual(schemaWithDeps.dependencies);
    });

    it('should preserve $ref and $defs', () => {
      const schemaWithRefs: RJSFSchema = {
        type: 'object',
        properties: {
          person: { $ref: '#/$defs/Person' },
        },
        $defs: {
          Person: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              age: { type: 'number' },
            },
          },
        },
      };

      const editedWithRefs = {
        ...schemaWithRefs,
        title: 'Person Schema',
      };

      const result = granularMergeEngine.mergeSchemas(schemaWithRefs, editedWithRefs);

      expect(result.success).toBe(true);
      expect(result.mergedSchema.$defs).toEqual(schemaWithRefs.$defs);
      expect(result.mergedSchema.properties?.person?.$ref).toBe('#/$defs/Person');
    });
  });

  describe('Validation and Error Handling', () => {
    it('should reject invalid schemas', () => {
      const invalidSchema = {
        // Missing type and other required properties
        properties: {},
      } as RJSFSchema;

      const result = granularMergeEngine.mergeSchemas(originalSchema, invalidSchema);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
    });

    it('should warn about type changes when not allowed', () => {
      editedSchema = {
        ...originalSchema,
        type: 'array', // Changed from object to array
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema, {
        allowTypeChanges: false,
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(error => error.includes('type changed'))).toBe(true);
    });

    it('should allow type changes when explicitly enabled', () => {
      editedSchema = {
        type: 'array',
        items: { type: 'string' },
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema, {
        allowTypeChanges: true,
      });

      expect(result.success).toBe(true);
      expect(result.mergedSchema.type).toBe('array');
    });
  });

  describe('Change Detection and Analysis', () => {
    it('should detect no changes when schemas are identical', () => {
      const result = granularMergeEngine.mergeSchemas(originalSchema, originalSchema);

      expect(result.success).toBe(true);
      expect(result.changes).toHaveLength(0);
      expect(result.warnings).toContain('No changes detected in schema');
    });

    it('should provide detailed change information', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          name: {
            type: 'string',
            title: 'Full Name',
            minLength: 2,
          },
        },
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.changes.length).toBeGreaterThan(0);
      expect(result.diff).toBeDefined();
      expect(result.diff?.analysis).toBeDefined();
    });

    it('should categorize changes correctly', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          name: {
            type: 'string',
            title: 'Full Name', // metadata change
          },
        },
        required: ['name', 'age'], // validation change
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema);

      expect(result.success).toBe(true);
      expect(result.diff?.analysis.changesByCategory).toBeDefined();
      expect(Object.keys(result.diff?.analysis.changesByCategory || {})).toContain('metadata');
    });
  });

  describe('Options and Configuration', () => {
    it('should respect preserveComplexConstructs option', () => {
      editedSchema = {
        type: 'object',
        properties: originalSchema.properties,
        // allOf removed in edited schema
      };

      const result = granularMergeEngine.mergeSchemas(originalSchema, editedSchema, {
        preserveComplexConstructs: true,
      });

      expect(result.success).toBe(true);
      expect(result.mergedSchema.allOf).toEqual(originalSchema.allOf);
      expect(result.warnings.some(w => w.includes('Preserved'))).toBe(true);
    });

    it('should skip validation when disabled', () => {
      const invalidSchema = {} as RJSFSchema;

      const result = granularMergeEngine.mergeSchemas(originalSchema, invalidSchema, {
        validateBeforeMerge: false,
      });

      // Should not fail validation since it's disabled
      expect(result.success).toBe(true);
    });
  });
});
