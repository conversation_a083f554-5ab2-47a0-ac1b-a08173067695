import { describe, it, expect, beforeEach } from 'vitest';
import { schemaDiffEngine } from '../schema-diff-engine';
import type { RJSFSchema } from '@rjsf/utils';

describe('SchemaDiffEngine', () => {
  let originalSchema: RJSFSchema;
  let editedSchema: RJSFSchema;

  beforeEach(() => {
    originalSchema = {
      type: 'object',
      title: 'Test Schema',
      properties: {
        name: {
          type: 'string',
          title: 'Name',
        },
        age: {
          type: 'number',
          title: 'Age',
        },
      },
      required: ['name'],
    };
  });

  describe('Basic Diff Detection', () => {
    it('should detect no changes when schemas are identical', () => {
      const diff = schemaDiffEngine.diffSchemas(originalSchema, originalSchema);

      expect(diff.isEmpty).toBe(true);
      expect(diff.changes).toHaveLength(0);
    });

    it('should detect property additions', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          email: {
            type: 'string',
            format: 'email',
          },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.isEmpty).toBe(false);
      expect(diff.changes).toHaveLength(1);
      expect(diff.changes[0].type).toBe('add');
      expect(diff.changes[0].path).toBe('properties.email');
    });

    it('should detect property removals', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          name: originalSchema.properties?.name,
          // age removed
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.isEmpty).toBe(false);
      expect(diff.changes).toHaveLength(1);
      expect(diff.changes[0].type).toBe('remove');
      expect(diff.changes[0].path).toBe('properties.age');
    });

    it('should detect property updates', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          name: {
            type: 'string',
            title: 'Full Name', // Changed title
            minLength: 2, // Added constraint
          },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.isEmpty).toBe(false);
      expect(diff.changes.length).toBeGreaterThan(0);
      
      const titleChange = diff.changes.find(c => c.path === 'properties.name.title');
      const minLengthChange = diff.changes.find(c => c.path === 'properties.name.minLength');
      
      expect(titleChange).toBeDefined();
      expect(titleChange?.type).toBe('update');
      expect(minLengthChange).toBeDefined();
      expect(minLengthChange?.type).toBe('add');
    });
  });

  describe('Complex Construct Detection', () => {
    it('should identify complex construct changes', () => {
      const schemaWithAllOf: RJSFSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
        },
        allOf: [
          { properties: { age: { type: 'number' } } },
        ],
      };

      const editedWithAllOf = {
        ...schemaWithAllOf,
        allOf: [
          { properties: { age: { type: 'number', minimum: 0 } } },
        ],
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithAllOf, editedWithAllOf);

      expect(diff.isEmpty).toBe(false);
      expect(diff.metadata.hasComplexConstructChanges).toBe(true);
      
      const allOfChange = diff.changes.find(c => c.path === 'allOf');
      expect(allOfChange?.isComplexConstruct).toBe(true);
    });

    it('should detect conditional schema changes', () => {
      const schemaWithConditional: RJSFSchema = {
        type: 'object',
        properties: {
          hasChildren: { type: 'boolean' },
        },
        if: { properties: { hasChildren: { const: true } } },
        then: { required: ['children'] },
      };

      const editedConditional = {
        ...schemaWithConditional,
        then: { required: ['children', 'childrenAges'] },
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithConditional, editedConditional);

      expect(diff.isEmpty).toBe(false);
      expect(diff.metadata.hasComplexConstructChanges).toBe(true);
      
      const thenChange = diff.changes.find(c => c.path === 'then');
      expect(thenChange?.isComplexConstruct).toBe(true);
    });

    it('should detect reference changes', () => {
      const schemaWithRef: RJSFSchema = {
        type: 'object',
        properties: {
          person: { $ref: '#/$defs/Person' },
        },
        $defs: {
          Person: {
            type: 'object',
            properties: { name: { type: 'string' } },
          },
        },
      };

      const editedWithRef = {
        ...schemaWithRef,
        properties: {
          person: { $ref: '#/$defs/UpdatedPerson' },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithRef, editedWithRef);

      expect(diff.isEmpty).toBe(false);
      
      const refChange = diff.changes.find(c => c.path === 'properties.person.$ref');
      expect(refChange?.isComplexConstruct).toBe(true);
    });
  });

  describe('Change Categorization', () => {
    it('should categorize type changes', () => {
      editedSchema = {
        ...originalSchema,
        type: 'array',
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.metadata.hasTypeChanges).toBe(true);
      
      const typeChange = diff.changes.find(c => c.path === 'type');
      expect(typeChange?.category).toBe('type');
    });

    it('should categorize property changes', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          email: { type: 'string' },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      const propertyChange = diff.changes.find(c => c.path === 'properties.email');
      expect(propertyChange?.category).toBe('properties');
    });

    it('should categorize validation changes', () => {
      editedSchema = {
        ...originalSchema,
        required: ['name', 'age'],
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      const requiredChange = diff.changes.find(c => c.path === 'required');
      expect(requiredChange?.category).toBe('validation');
    });

    it('should categorize metadata changes', () => {
      editedSchema = {
        ...originalSchema,
        title: 'Updated Test Schema',
        description: 'A test schema for validation',
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      const titleChange = diff.changes.find(c => c.path === 'title');
      const descChange = diff.changes.find(c => c.path === 'description');
      
      expect(titleChange?.category).toBe('metadata');
      expect(descChange?.category).toBe('metadata');
    });
  });

  describe('Impact Assessment', () => {
    it('should assess type changes as breaking', () => {
      editedSchema = {
        ...originalSchema,
        properties: {
          ...originalSchema.properties,
          age: {
            type: 'string', // Changed from number to string
          },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      const typeChange = diff.changes.find(c => c.path === 'properties.age.type');
      expect(typeChange?.impact).toBe('breaking');
    });

    it('should assess complex construct changes as significant', () => {
      const schemaWithAllOf: RJSFSchema = {
        type: 'object',
        allOf: [{ properties: { name: { type: 'string' } } }],
      };

      const editedWithAllOf = {
        type: 'object',
        allOf: [{ properties: { name: { type: 'string', minLength: 1 } } }],
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithAllOf, editedWithAllOf);

      const allOfChange = diff.changes.find(c => c.isComplexConstruct);
      expect(allOfChange?.impact).toBe('significant');
    });

    it('should assess metadata changes as minor', () => {
      editedSchema = {
        ...originalSchema,
        title: 'Updated Title',
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      const titleChange = diff.changes.find(c => c.path === 'title');
      expect(titleChange?.impact).toBe('minor');
    });
  });

  describe('Analysis and Recommendations', () => {
    it('should provide risk assessment', () => {
      editedSchema = {
        ...originalSchema,
        type: 'array', // Breaking change
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.analysis.riskLevel).toBe('high');
      expect(diff.analysis.recommendations).toContain('Type changes detected. Verify form data compatibility.');
    });

    it('should generate change summary', () => {
      editedSchema = {
        ...originalSchema,
        title: 'Updated Title',
        properties: {
          ...originalSchema.properties,
          email: { type: 'string' },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.analysis.summary).toContain('addition');
      expect(diff.analysis.summary).toContain('update');
    });

    it('should group changes by category', () => {
      editedSchema = {
        ...originalSchema,
        title: 'Updated Title',
        description: 'Updated description',
        properties: {
          ...originalSchema.properties,
          email: { type: 'string' },
        },
      };

      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      expect(diff.analysis.changesByCategory.metadata).toHaveLength(2);
      expect(diff.analysis.changesByCategory.properties).toHaveLength(1);
    });

    it('should identify affected complex constructs', () => {
      const schemaWithComplexConstructs: RJSFSchema = {
        type: 'object',
        allOf: [{ properties: { name: { type: 'string' } } }],
        if: { properties: { name: { minLength: 1 } } },
        then: { required: ['name'] },
      };

      const editedComplex = {
        ...schemaWithComplexConstructs,
        allOf: [{ properties: { name: { type: 'string', maxLength: 100 } } }],
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithComplexConstructs, editedComplex);

      expect(diff.analysis.complexConstructsAffected).toContain('allOf');
    });
  });

  describe('Array Handling', () => {
    it('should detect schema array changes', () => {
      const schemaWithArrays: RJSFSchema = {
        type: 'object',
        allOf: [
          { properties: { name: { type: 'string' } } },
          { properties: { age: { type: 'number' } } },
        ],
      };

      const editedArrays = {
        type: 'object',
        allOf: [
          { properties: { name: { type: 'string' } } },
          { properties: { age: { type: 'number', minimum: 0 } } },
          { properties: { email: { type: 'string' } } },
        ],
      };

      const diff = schemaDiffEngine.diffSchemas(schemaWithArrays, editedArrays);

      expect(diff.isEmpty).toBe(false);
      expect(diff.metadata.hasComplexConstructChanges).toBe(true);
    });
  });
});
