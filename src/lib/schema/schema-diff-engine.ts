import type { RJSFSchema } from "@rjsf/utils";
import { isEqual, cloneDeep } from 'lodash-es';

/**
 * Advanced Schema Diff Engine
 * 
 * Provides deep comparison capabilities specifically designed for JSON Schema,
 * with special handling for complex constructs and semantic understanding
 * of schema relationships.
 */
export class SchemaDiffEngine {
  
  /**
   * Performs deep diff between two schemas with semantic understanding
   */
  public diffSchemas(originalSchema: RJSFSchema, editedSchema: RJSFSchema): SchemaDiff {
    const changes: SchemaChange[] = [];
    const metadata: DiffMetadata = {
      hasComplexConstructChanges: false,
      hasTypeChanges: false,
      hasStructuralChanges: false,
      affectedPaths: new Set(),
    };

    // Perform the diff
    this.diffObjects(originalSchema, editedSchema, '', changes, metadata);

    // Analyze the changes
    const analysis = this.analyzeChanges(changes, metadata);

    return {
      changes,
      metadata,
      analysis,
      isEmpty: changes.length === 0,
    };
  }

  /**
   * Recursively diffs two objects with schema-aware logic
   */
  private diffObjects(
    original: any,
    edited: any,
    path: string,
    changes: SchemaChange[],
    metadata: DiffMetadata
  ): void {
    // Handle null/undefined cases
    if (original === null || original === undefined || edited === null || edited === undefined) {
      if (original !== edited) {
        this.addChange(changes, metadata, {
          type: 'update',
          path,
          oldValue: original,
          newValue: edited,
        });
      }
      return;
    }

    // Handle primitive values
    if (this.isPrimitive(original) || this.isPrimitive(edited)) {
      if (!isEqual(original, edited)) {
        this.addChange(changes, metadata, {
          type: 'update',
          path,
          oldValue: original,
          newValue: edited,
        });
      }
      return;
    }

    // Handle arrays with semantic understanding
    if (Array.isArray(original) || Array.isArray(edited)) {
      this.diffArrays(original, edited, path, changes, metadata);
      return;
    }

    // Handle objects
    if (typeof original === 'object' && typeof edited === 'object') {
      this.diffObjectProperties(original, edited, path, changes, metadata);
    }
  }

  /**
   * Diffs arrays with special handling for schema arrays
   */
  private diffArrays(
    original: any,
    edited: any,
    path: string,
    changes: SchemaChange[],
    metadata: DiffMetadata
  ): void {
    // If one is array and other isn't, it's a type change
    if (Array.isArray(original) !== Array.isArray(edited)) {
      this.addChange(changes, metadata, {
        type: 'update',
        path,
        oldValue: original,
        newValue: edited,
      });
      return;
    }

    // Both are arrays - check if they're semantically different
    if (!isEqual(original, edited)) {
      // For schema arrays (like in allOf, oneOf, anyOf), we need special handling
      if (this.isSchemaArray(path)) {
        this.diffSchemaArray(original, edited, path, changes, metadata);
      } else {
        // Regular array diff
        this.addChange(changes, metadata, {
          type: 'update',
          path,
          oldValue: original,
          newValue: edited,
        });
      }
    }
  }

  /**
   * Diffs schema arrays (allOf, oneOf, anyOf) with semantic understanding
   */
  private diffSchemaArray(
    original: RJSFSchema[],
    edited: RJSFSchema[],
    path: string,
    changes: SchemaChange[],
    metadata: DiffMetadata
  ): void {
    // For now, treat schema arrays as atomic units
    // Future enhancement: could do element-wise comparison
    this.addChange(changes, metadata, {
      type: 'update',
      path,
      oldValue: original,
      newValue: edited,
    });
  }

  /**
   * Diffs object properties with schema-aware logic
   */
  private diffObjectProperties(
    original: Record<string, any>,
    edited: Record<string, any>,
    path: string,
    changes: SchemaChange[],
    metadata: DiffMetadata
  ): void {
    const allKeys = new Set([...Object.keys(original), ...Object.keys(edited)]);

    for (const key of allKeys) {
      const newPath = path ? `${path}.${key}` : key;
      const originalValue = original[key];
      const editedValue = edited[key];

      if (!(key in original)) {
        // Property added
        this.addChange(changes, metadata, {
          type: 'add',
          path: newPath,
          oldValue: undefined,
          newValue: editedValue,
        });
      } else if (!(key in edited)) {
        // Property removed
        this.addChange(changes, metadata, {
          type: 'remove',
          path: newPath,
          oldValue: originalValue,
          newValue: undefined,
        });
      } else {
        // Property potentially modified - recurse
        this.diffObjects(originalValue, editedValue, newPath, changes, metadata);
      }
    }
  }

  /**
   * Adds a change and updates metadata
   */
  private addChange(
    changes: SchemaChange[],
    metadata: DiffMetadata,
    change: Omit<SchemaChange, 'isComplexConstruct' | 'category' | 'impact'>
  ): void {
    const isComplexConstruct = this.isComplexConstructPath(change.path);
    const category = this.categorizeChange(change.path);
    const impact = this.assessImpact(change, isComplexConstruct);

    const fullChange: SchemaChange = {
      ...change,
      isComplexConstruct,
      category,
      impact,
    };

    changes.push(fullChange);

    // Update metadata
    metadata.affectedPaths.add(change.path);
    if (isComplexConstruct) {
      metadata.hasComplexConstructChanges = true;
    }
    if (category === 'type') {
      metadata.hasTypeChanges = true;
    }
    if (category === 'structure') {
      metadata.hasStructuralChanges = true;
    }
  }

  /**
   * Analyzes changes to provide insights
   */
  private analyzeChanges(changes: SchemaChange[], metadata: DiffMetadata): ChangeAnalysis {
    const riskLevel = this.assessRiskLevel(changes, metadata);
    const recommendations = this.generateRecommendations(changes, metadata);
    const summary = this.generateSummary(changes);

    return {
      riskLevel,
      recommendations,
      summary,
      changesByCategory: this.groupChangesByCategory(changes),
      complexConstructsAffected: changes.filter(c => c.isComplexConstruct).map(c => c.path),
    };
  }

  /**
   * Assesses the risk level of the changes
   */
  private assessRiskLevel(changes: SchemaChange[], metadata: DiffMetadata): 'low' | 'medium' | 'high' {
    if (metadata.hasTypeChanges) return 'high';
    if (metadata.hasComplexConstructChanges) return 'medium';
    if (changes.some(c => c.impact === 'breaking')) return 'high';
    if (changes.some(c => c.impact === 'significant')) return 'medium';
    return 'low';
  }

  /**
   * Generates recommendations based on changes
   */
  private generateRecommendations(changes: SchemaChange[], metadata: DiffMetadata): string[] {
    const recommendations: string[] = [];

    if (metadata.hasTypeChanges) {
      recommendations.push('Type changes detected. Verify form data compatibility.');
    }

    if (metadata.hasComplexConstructChanges) {
      recommendations.push('Complex constructs modified. Review validation logic carefully.');
    }

    const breakingChanges = changes.filter(c => c.impact === 'breaking');
    if (breakingChanges.length > 0) {
      recommendations.push(`${breakingChanges.length} breaking changes detected. Test thoroughly.`);
    }

    return recommendations;
  }

  /**
   * Generates a summary of changes
   */
  private generateSummary(changes: SchemaChange[]): string {
    const adds = changes.filter(c => c.type === 'add').length;
    const removes = changes.filter(c => c.type === 'remove').length;
    const updates = changes.filter(c => c.type === 'update').length;

    const parts: string[] = [];
    if (adds > 0) parts.push(`${adds} addition${adds > 1 ? 's' : ''}`);
    if (removes > 0) parts.push(`${removes} removal${removes > 1 ? 's' : ''}`);
    if (updates > 0) parts.push(`${updates} update${updates > 1 ? 's' : ''}`);

    return parts.join(', ') || 'No changes';
  }

  /**
   * Groups changes by category
   */
  private groupChangesByCategory(changes: SchemaChange[]): Record<string, SchemaChange[]> {
    const groups: Record<string, SchemaChange[]> = {};
    
    for (const change of changes) {
      if (!groups[change.category]) {
        groups[change.category] = [];
      }
      groups[change.category].push(change);
    }

    return groups;
  }

  /**
   * Categorizes a change based on its path
   */
  private categorizeChange(path: string): string {
    if (path === 'type' || path.endsWith('.type')) return 'type';
    if (path === 'properties' || path.includes('.properties.')) return 'properties';
    if (path.includes('allOf') || path.includes('oneOf') || path.includes('anyOf')) return 'composition';
    if (path.includes('if') || path.includes('then') || path.includes('else')) return 'conditional';
    if (path.includes('required')) return 'validation';
    if (path.includes('dependencies')) return 'dependencies';
    if (path === 'title' || path === 'description' || path.endsWith('.title') || path.endsWith('.description')) return 'metadata';
    return 'structure';
  }

  /**
   * Assesses the impact of a change
   */
  private assessImpact(change: Omit<SchemaChange, 'isComplexConstruct' | 'category' | 'impact'>, isComplexConstruct: boolean): 'minor' | 'significant' | 'breaking' {
    // Type changes are always breaking
    if (change.path === 'type' || change.path.endsWith('.type')) {
      return 'breaking';
    }

    // Complex construct changes are significant
    if (isComplexConstruct) {
      return 'significant';
    }

    // Required field changes are significant
    if (change.path.includes('required')) {
      return 'significant';
    }

    // Removing properties is significant
    if (change.type === 'remove' && change.path.includes('properties')) {
      return 'significant';
    }

    return 'minor';
  }

  /**
   * Checks if a value is primitive
   */
  private isPrimitive(value: any): boolean {
    return value === null || typeof value !== 'object';
  }

  /**
   * Checks if a path represents a complex JSON Schema construct
   */
  private isComplexConstructPath(path: string): boolean {
    const complexKeywords = [
      'allOf', 'oneOf', 'anyOf', 'not',
      'if', 'then', 'else',
      'dependencies', 'dependentSchemas', 'dependentRequired',
      'patternProperties', 'additionalProperties', 'additionalItems',
      'contains', 'propertyNames', 'unevaluatedProperties', 'unevaluatedItems',
      '$ref', '$defs', 'definitions'
    ];

    return complexKeywords.some(keyword => path.includes(keyword));
  }

  /**
   * Checks if a path represents a schema array
   */
  private isSchemaArray(path: string): boolean {
    return path.includes('allOf') || path.includes('oneOf') || path.includes('anyOf');
  }
}

// Types
export interface SchemaDiff {
  changes: SchemaChange[];
  metadata: DiffMetadata;
  analysis: ChangeAnalysis;
  isEmpty: boolean;
}

export interface SchemaChange {
  type: 'add' | 'remove' | 'update';
  path: string;
  oldValue: any;
  newValue: any;
  isComplexConstruct: boolean;
  category: string;
  impact: 'minor' | 'significant' | 'breaking';
}

export interface DiffMetadata {
  hasComplexConstructChanges: boolean;
  hasTypeChanges: boolean;
  hasStructuralChanges: boolean;
  affectedPaths: Set<string>;
}

export interface ChangeAnalysis {
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
  summary: string;
  changesByCategory: Record<string, SchemaChange[]>;
  complexConstructsAffected: string[];
}

// Export singleton instance
export const schemaDiffEngine = new SchemaDiffEngine();
