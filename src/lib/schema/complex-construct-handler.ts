import type { RJSFSchema } from "@rjsf/utils";
import { cloneDeep, isEqual } from 'lodash-es';

/**
 * Complex Construct Preservation System
 * 
 * Handles all advanced JSON Schema features with specialized logic
 * for each construct type to ensure proper preservation during merging.
 */
export class ComplexConstructHandler {

  /**
   * Extracts all complex constructs from a schema
   */
  public extractComplexConstructs(schema: RJSFSchema): ComplexConstructs {
    const constructs: ComplexConstructs = {};

    // Composition constructs
    if (schema.allOf) constructs.allOf = cloneDeep(schema.allOf);
    if (schema.oneOf) constructs.oneOf = cloneDeep(schema.oneOf);
    if (schema.anyOf) constructs.anyOf = cloneDeep(schema.anyOf);
    if (schema.not) constructs.not = cloneDeep(schema.not);

    // Conditional constructs
    if (schema.if) constructs.if = cloneDeep(schema.if);
    if (schema.then) constructs.then = cloneDeep(schema.then);
    if (schema.else) constructs.else = cloneDeep(schema.else);

    // Dependency constructs
    if (schema.dependencies) constructs.dependencies = cloneDeep(schema.dependencies);
    if (schema.dependentSchemas) constructs.dependentSchemas = cloneDeep(schema.dependentSchemas);
    if (schema.dependentRequired) constructs.dependentRequired = cloneDeep(schema.dependentRequired);

    // Property constructs
    if (schema.patternProperties) constructs.patternProperties = cloneDeep(schema.patternProperties);
    if (schema.additionalProperties !== undefined) constructs.additionalProperties = cloneDeep(schema.additionalProperties);
    if (schema.additionalItems !== undefined) constructs.additionalItems = cloneDeep(schema.additionalItems);
    if (schema.unevaluatedProperties !== undefined) constructs.unevaluatedProperties = cloneDeep(schema.unevaluatedProperties);
    if (schema.unevaluatedItems !== undefined) constructs.unevaluatedItems = cloneDeep(schema.unevaluatedItems);

    // Content constructs
    if (schema.contains) constructs.contains = cloneDeep(schema.contains);
    if (schema.propertyNames) constructs.propertyNames = cloneDeep(schema.propertyNames);

    // Reference constructs
    if (schema.$ref) constructs.$ref = schema.$ref;
    if (schema.$defs) constructs.$defs = cloneDeep(schema.$defs);
    if (schema.definitions) constructs.definitions = cloneDeep(schema.definitions);

    // Meta constructs
    if (schema.$id) constructs.$id = schema.$id;
    if (schema.$schema) constructs.$schema = schema.$schema;
    if (schema.$comment) constructs.$comment = schema.$comment;

    // Validation constructs
    if (schema.const !== undefined) constructs.const = cloneDeep(schema.const);
    if (schema.examples) constructs.examples = cloneDeep(schema.examples);

    return constructs;
  }

  /**
   * Applies complex constructs to a schema
   */
  public applyComplexConstructs(schema: RJSFSchema, constructs: ComplexConstructs): RJSFSchema {
    const result = cloneDeep(schema);

    // Apply composition constructs
    if (constructs.allOf) result.allOf = cloneDeep(constructs.allOf);
    if (constructs.oneOf) result.oneOf = cloneDeep(constructs.oneOf);
    if (constructs.anyOf) result.anyOf = cloneDeep(constructs.anyOf);
    if (constructs.not) result.not = cloneDeep(constructs.not);

    // Apply conditional constructs
    if (constructs.if) result.if = cloneDeep(constructs.if);
    if (constructs.then) result.then = cloneDeep(constructs.then);
    if (constructs.else) result.else = cloneDeep(constructs.else);

    // Apply dependency constructs
    if (constructs.dependencies) result.dependencies = cloneDeep(constructs.dependencies);
    if (constructs.dependentSchemas) result.dependentSchemas = cloneDeep(constructs.dependentSchemas);
    if (constructs.dependentRequired) result.dependentRequired = cloneDeep(constructs.dependentRequired);

    // Apply property constructs
    if (constructs.patternProperties) result.patternProperties = cloneDeep(constructs.patternProperties);
    if (constructs.additionalProperties !== undefined) result.additionalProperties = cloneDeep(constructs.additionalProperties);
    if (constructs.additionalItems !== undefined) result.additionalItems = cloneDeep(constructs.additionalItems);
    if (constructs.unevaluatedProperties !== undefined) result.unevaluatedProperties = cloneDeep(constructs.unevaluatedProperties);
    if (constructs.unevaluatedItems !== undefined) result.unevaluatedItems = cloneDeep(constructs.unevaluatedItems);

    // Apply content constructs
    if (constructs.contains) result.contains = cloneDeep(constructs.contains);
    if (constructs.propertyNames) result.propertyNames = cloneDeep(constructs.propertyNames);

    // Apply reference constructs
    if (constructs.$ref) result.$ref = constructs.$ref;
    if (constructs.$defs) result.$defs = cloneDeep(constructs.$defs);
    if (constructs.definitions) result.definitions = cloneDeep(constructs.definitions);

    // Apply meta constructs
    if (constructs.$id) result.$id = constructs.$id;
    if (constructs.$schema) result.$schema = constructs.$schema;
    if (constructs.$comment) result.$comment = constructs.$comment;

    // Apply validation constructs
    if (constructs.const !== undefined) result.const = cloneDeep(constructs.const);
    if (constructs.examples) result.examples = cloneDeep(constructs.examples);

    return result;
  }

  /**
   * Merges complex constructs intelligently
   */
  public mergeComplexConstructs(
    originalConstructs: ComplexConstructs,
    editedConstructs: ComplexConstructs,
    options: MergeConstructOptions = {}
  ): ComplexConstructMergeResult {
    const { preserveOriginal = true, allowDestructiveChanges = false } = options;
    const warnings: string[] = [];
    const mergedConstructs: ComplexConstructs = {};

    // Get all construct keys
    const allKeys = new Set([
      ...Object.keys(originalConstructs),
      ...Object.keys(editedConstructs)
    ]);

    for (const key of allKeys) {
      const originalValue = originalConstructs[key as keyof ComplexConstructs];
      const editedValue = editedConstructs[key as keyof ComplexConstructs];

      if (originalValue === undefined && editedValue !== undefined) {
        // New construct added
        mergedConstructs[key as keyof ComplexConstructs] = cloneDeep(editedValue);
      } else if (originalValue !== undefined && editedValue === undefined) {
        // Construct removed
        if (preserveOriginal) {
          mergedConstructs[key as keyof ComplexConstructs] = cloneDeep(originalValue);
          warnings.push(`Preserved ${key} construct that was removed in edited schema`);
        }
      } else if (originalValue !== undefined && editedValue !== undefined) {
        // Construct potentially modified
        if (!isEqual(originalValue, editedValue)) {
          if (allowDestructiveChanges) {
            mergedConstructs[key as keyof ComplexConstructs] = cloneDeep(editedValue);
            warnings.push(`Modified ${key} construct - please review carefully`);
          } else {
            mergedConstructs[key as keyof ComplexConstructs] = cloneDeep(originalValue);
            warnings.push(`Preserved original ${key} construct to prevent destructive changes`);
          }
        } else {
          // No change
          mergedConstructs[key as keyof ComplexConstructs] = cloneDeep(originalValue);
        }
      }
    }

    return {
      mergedConstructs,
      warnings,
      hasChanges: !isEqual(originalConstructs, mergedConstructs),
    };
  }

  /**
   * Validates complex constructs for consistency
   */
  public validateComplexConstructs(constructs: ComplexConstructs): ConstructValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate composition constructs
    if (constructs.allOf && constructs.oneOf) {
      warnings.push('Schema has both allOf and oneOf - this may create complex validation logic');
    }

    if (constructs.allOf && constructs.anyOf) {
      warnings.push('Schema has both allOf and anyOf - this may create complex validation logic');
    }

    // Validate conditional constructs
    if (constructs.if && !constructs.then && !constructs.else) {
      warnings.push('Schema has if without then or else - condition will have no effect');
    }

    if ((constructs.then || constructs.else) && !constructs.if) {
      errors.push('Schema has then/else without if - this is invalid');
    }

    // Validate references
    if (constructs.$ref && Object.keys(constructs).length > 1) {
      warnings.push('Schema has $ref with other properties - other properties may be ignored');
    }

    // Validate dependencies
    if (constructs.dependencies && constructs.dependentSchemas) {
      warnings.push('Schema has both dependencies and dependentSchemas - use dependentSchemas for new schemas');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Checks if a schema has any complex constructs
   */
  public hasComplexConstructs(schema: RJSFSchema): boolean {
    const complexKeys = [
      'allOf', 'oneOf', 'anyOf', 'not',
      'if', 'then', 'else',
      'dependencies', 'dependentSchemas', 'dependentRequired',
      'patternProperties', 'additionalProperties', 'additionalItems',
      'unevaluatedProperties', 'unevaluatedItems',
      'contains', 'propertyNames',
      '$ref', '$defs', 'definitions',
      'const', 'examples'
    ];

    return complexKeys.some(key => key in schema);
  }

  /**
   * Gets a list of complex construct types present in a schema
   */
  public getComplexConstructTypes(schema: RJSFSchema): string[] {
    const types: string[] = [];
    const complexKeys = [
      'allOf', 'oneOf', 'anyOf', 'not',
      'if', 'then', 'else',
      'dependencies', 'dependentSchemas', 'dependentRequired',
      'patternProperties', 'additionalProperties', 'additionalItems',
      'unevaluatedProperties', 'unevaluatedItems',
      'contains', 'propertyNames',
      '$ref', '$defs', 'definitions',
      'const', 'examples'
    ];

    for (const key of complexKeys) {
      if (key in schema) {
        types.push(key);
      }
    }

    return types;
  }
}

// Types
export interface ComplexConstructs {
  // Composition
  allOf?: RJSFSchema[];
  oneOf?: RJSFSchema[];
  anyOf?: RJSFSchema[];
  not?: RJSFSchema;

  // Conditional
  if?: RJSFSchema;
  then?: RJSFSchema;
  else?: RJSFSchema;

  // Dependencies
  dependencies?: Record<string, RJSFSchema | string[]>;
  dependentSchemas?: Record<string, RJSFSchema>;
  dependentRequired?: Record<string, string[]>;

  // Properties
  patternProperties?: Record<string, RJSFSchema>;
  additionalProperties?: boolean | RJSFSchema;
  additionalItems?: boolean | RJSFSchema;
  unevaluatedProperties?: boolean | RJSFSchema;
  unevaluatedItems?: boolean | RJSFSchema;

  // Content
  contains?: RJSFSchema;
  propertyNames?: RJSFSchema;

  // References
  $ref?: string;
  $defs?: Record<string, RJSFSchema>;
  definitions?: Record<string, RJSFSchema>;

  // Meta
  $id?: string;
  $schema?: string;
  $comment?: string;

  // Validation
  const?: any;
  examples?: any[];
}

export interface MergeConstructOptions {
  preserveOriginal?: boolean;
  allowDestructiveChanges?: boolean;
}

export interface ComplexConstructMergeResult {
  mergedConstructs: ComplexConstructs;
  warnings: string[];
  hasChanges: boolean;
}

export interface ConstructValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Export singleton instance
export const complexConstructHandler = new ComplexConstructHandler();
