import type { RJSFSchema } from "@rjsf/utils";
import { cloneDeep } from 'lodash-es';
import { schemaDiffEngine, type SchemaDiff } from './schema-diff-engine';
import { complexConstructHandler } from './complex-construct-handler';

/**
 * Comprehensive Granular Schema Merge Engine
 * 
 * This engine provides intelligent merging of JSON Schemas while preserving
 * all complex constructs like allOf, oneOf, anyOf, if/then/else, dependencies,
 * patternProperties, $ref, $defs, and other advanced JSON Schema features.
 */
export class GranularSchemaMergeEngine {
  
  /**
   * Performs intelligent granular merge of schemas with advanced construct preservation
   */
  public mergeSchemas(
    originalSchema: RJSFSchema,
    editedSchema: RJSFSchema,
    options: MergeOptions = {}
  ): MergeResult {
    const {
      preserveComplexConstructs = true,
      validateBeforeMerge = true,
      allowTypeChanges = false,
      preserveMetadata = true,
    } = options;

    try {
      // Validate inputs
      if (validateBeforeMerge) {
        const validation = this.validateSchemas(originalSchema, editedSchema, { allowTypeChanges });
        if (!validation.valid) {
          return {
            success: false,
            mergedSchema: originalSchema,
            errors: validation.errors,
            warnings: [],
            changes: [],
            diff: null,
          };
        }
      }

      // Perform advanced diff analysis
      const diff = schemaDiffEngine.diffSchemas(originalSchema, editedSchema);

      if (diff.isEmpty) {
        return {
          success: true,
          mergedSchema: originalSchema,
          errors: [],
          warnings: ['No changes detected in schema'],
          changes: [],
          diff,
        };
      }

      // Extract complex constructs from both schemas
      const originalConstructs = complexConstructHandler.extractComplexConstructs(originalSchema);
      const editedConstructs = complexConstructHandler.extractComplexConstructs(editedSchema);

      // Create base merged schema (without complex constructs)
      let mergedSchema = this.createBaseMergedSchema(originalSchema, editedSchema, diff);
      const warnings: string[] = [...diff.analysis.recommendations];

      // Handle complex constructs preservation
      if (preserveComplexConstructs) {
        const constructMergeResult = complexConstructHandler.mergeComplexConstructs(
          originalConstructs,
          editedConstructs,
          {
            preserveOriginal: true,
            allowDestructiveChanges: allowTypeChanges,
          }
        );

        // Apply merged complex constructs
        mergedSchema = complexConstructHandler.applyComplexConstructs(
          mergedSchema,
          constructMergeResult.mergedConstructs
        );

        warnings.push(...constructMergeResult.warnings);
      }

      // Final validation
      const finalValidation = this.validateMergedSchema(mergedSchema);
      if (!finalValidation.valid) {
        return {
          success: false,
          mergedSchema: originalSchema,
          errors: finalValidation.errors,
          warnings,
          changes: diff.changes,
          diff,
        };
      }

      return {
        success: true,
        mergedSchema,
        errors: [],
        warnings,
        changes: diff.changes,
        diff,
      };
    } catch (error) {
      return {
        success: false,
        mergedSchema: originalSchema,
        errors: [`Merge failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
        changes: [],
        diff: null,
      };
    }
  }

  /**
   * Creates a base merged schema by applying non-complex-construct changes
   */
  private createBaseMergedSchema(
    originalSchema: RJSFSchema,
    editedSchema: RJSFSchema,
    diff: SchemaDiff
  ): RJSFSchema {
    const mergedSchema = cloneDeep(originalSchema);

    // Apply changes that are not complex constructs
    for (const change of diff.changes) {
      if (!change.isComplexConstruct) {
        this.applyChangeToSchema(mergedSchema, change);
      }
    }

    return mergedSchema;
  }

  /**
   * Applies a single change to a schema
   */
  private applyChangeToSchema(schema: RJSFSchema, change: { type: string; path: string; newValue: unknown }): void {
    const pathParts = change.path.split('.');
    let current: any = schema;

    // Navigate to parent
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (!(part in current)) {
        current[part] = {};
      }
      current = current[part];
    }

    const finalKey = pathParts[pathParts.length - 1];

    // Apply the change
    switch (change.type) {
      case 'add':
      case 'update':
        current[finalKey] = change.newValue;
        break;
      case 'remove':
        delete current[finalKey];
        break;
    }
  }



  /**
   * Validates schemas before merging
   */
  private validateSchemas(
    original: RJSFSchema,
    edited: RJSFSchema,
    options: { allowTypeChanges: boolean }
  ): ValidationResult {
    const errors: string[] = [];

    // Basic structure validation
    if (!original || !edited || typeof original !== 'object' || typeof edited !== 'object') {
      errors.push('Both schemas must be valid objects');
      return { valid: false, errors };
    }

    // Type consistency check
    if (!options.allowTypeChanges && original.type && edited.type && original.type !== edited.type) {
      errors.push(`Root type changed from ${original.type} to ${edited.type}. This may break existing form data.`);
    }

    // Check for required schema properties
    if (!edited.type && !edited.properties && !edited.allOf && !edited.oneOf && !edited.anyOf) {
      errors.push('Schema must have a type or complex construct (allOf, oneOf, anyOf)');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Validates the final merged schema
   */
  private validateMergedSchema(schema: RJSFSchema): ValidationResult {
    const errors: string[] = [];

    try {
      // Check for circular references
      JSON.stringify(schema);
      
      // Basic JSON Schema validation
      if (!schema.type && !schema.allOf && !schema.oneOf && !schema.anyOf) {
        errors.push('Schema must have a type or complex construct');
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('circular')) {
        errors.push('Schema contains circular references');
      } else {
        errors.push(`Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { valid: errors.length === 0, errors };
  }


}

// Types
export interface MergeOptions {
  preserveComplexConstructs?: boolean;
  validateBeforeMerge?: boolean;
  allowTypeChanges?: boolean;
  preserveMetadata?: boolean;
}

export interface MergeResult {
  success: boolean;
  mergedSchema: RJSFSchema;
  errors: string[];
  warnings: string[];
  changes: SchemaChange[];
  diff: SchemaDiff | null;
}

export interface SchemaChange {
  type: 'add' | 'remove' | 'update';
  path: string;
  oldValue: unknown;
  newValue: unknown;
  isComplexConstruct: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

// Export singleton instance
export const granularMergeEngine = new GranularSchemaMergeEngine();
