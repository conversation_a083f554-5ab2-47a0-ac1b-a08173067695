import { describe, it, expect, beforeEach } from 'vitest';
import { useSchemaGraphStore } from '../schema-graph';
import type { FieldNode } from '../../graph/schema-graph-engine';
import type { RJSFSchema } from '@rjsf/utils';

describe('useSchemaGraphStore', () => {
  beforeEach(() => {
    // Reset store to initial state
    useSchemaGraphStore.setState({
      graph: {
        nodes: {
          root: {
            id: 'root',
            key: 'root',
            type: 'object',
            title: 'Root',
            children: [],
          },
        },
        rootId: 'root',
      },
    });
  });

  describe('addNode', () => {
    it('should add a new node and return its ID', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Test Field',
      };

      const nodeId = useSchemaGraphStore.getState().addNode(nodeData);

      expect(nodeId).toBeTruthy();
      const graph = useSchemaGraphStore.getState().graph;
      expect(graph.nodes[nodeId]).toBeDefined();
      expect(graph.nodes[nodeId].title).toBe('Test Field');
      expect(graph.nodes.root.children).toContain(nodeId);
    });

    it('should add node to specified parent', () => {
      const parentData: Omit<FieldNode, 'id'> = {
        key: 'parent',
        type: 'object',
        title: 'Parent',
      };

      const childData: Omit<FieldNode, 'id'> = {
        key: 'child',
        type: 'string',
        title: 'Child',
      };

      const parentId = useSchemaGraphStore.getState().addNode(parentData);
      const childId = useSchemaGraphStore.getState().addNode(childData, parentId);

      const graph = useSchemaGraphStore.getState().graph;
      expect(graph.nodes[childId].parentId).toBe(parentId);
      expect(graph.nodes[parentId].children).toContain(childId);
    });
  });

  describe('removeNode', () => {
    it('should remove a node and update parent', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Test Field',
      };

      const nodeId = useSchemaGraphStore.getState().addNode(nodeData);
      expect(useSchemaGraphStore.getState().graph.nodes[nodeId]).toBeDefined();

      useSchemaGraphStore.getState().removeNode(nodeId);

      const graph = useSchemaGraphStore.getState().graph;
      expect(graph.nodes[nodeId]).toBeUndefined();
      expect(graph.nodes.root.children).not.toContain(nodeId);
    });
  });

  describe('updateNode', () => {
    it('should update node properties', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Original Title',
      };

      const nodeId = useSchemaGraphStore.getState().addNode(nodeData);

      useSchemaGraphStore.getState().updateNode(nodeId, {
        title: 'Updated Title',
        description: 'New description',
      });

      const updatedNode = useSchemaGraphStore.getState().graph.nodes[nodeId];
      expect(updatedNode.title).toBe('Updated Title');
      expect(updatedNode.description).toBe('New description');
      expect(updatedNode.type).toBe('string'); // Should preserve other properties
    });
  });

  describe('moveNode', () => {
    it('should move a node to a new parent', () => {
      const parent1Data: Omit<FieldNode, 'id'> = {
        key: 'parent1',
        type: 'object',
        title: 'Parent 1',
      };

      const parent2Data: Omit<FieldNode, 'id'> = {
        key: 'parent2',
        type: 'object',
        title: 'Parent 2',
      };

      const childData: Omit<FieldNode, 'id'> = {
        key: 'child',
        type: 'string',
        title: 'Child',
      };

      const parent1Id = useSchemaGraphStore.getState().addNode(parent1Data);
      const parent2Id = useSchemaGraphStore.getState().addNode(parent2Data);
      const childId = useSchemaGraphStore.getState().addNode(childData, parent1Id);

      useSchemaGraphStore.getState().moveNode(childId, parent2Id);

      const graph = useSchemaGraphStore.getState().graph;
      expect(graph.nodes[childId].parentId).toBe(parent2Id);
      expect(graph.nodes[parent1Id].children).not.toContain(childId);
      expect(graph.nodes[parent2Id].children).toContain(childId);
    });
  });

  describe('reorderNode', () => {
    it('should reorder nodes within parent', () => {
      const child1Data: Omit<FieldNode, 'id'> = { key: 'child1', type: 'string', title: 'Child 1' };
      const child2Data: Omit<FieldNode, 'id'> = { key: 'child2', type: 'string', title: 'Child 2' };
      const child3Data: Omit<FieldNode, 'id'> = { key: 'child3', type: 'string', title: 'Child 3' };

      const child1Id = useSchemaGraphStore.getState().addNode(child1Data);
      useSchemaGraphStore.getState().addNode(child2Data);
      useSchemaGraphStore.getState().addNode(child3Data);

      const originalOrder = [...useSchemaGraphStore.getState().graph.nodes.root.children!];

      useSchemaGraphStore.getState().reorderNode(child1Id, 2); // Move first to last

      const newOrder = useSchemaGraphStore.getState().graph.nodes.root.children!;
      expect(newOrder[2]).toBe(child1Id);
      expect(newOrder).not.toEqual(originalOrder);
    });
  });

  describe('setSchemaFromJson', () => {
    it('should import JSON schema and replace current graph', () => {
      const inputSchema: RJSFSchema = {
        type: 'object',
        title: 'Person',
        properties: {
          name: {
            type: 'string',
            title: 'Full Name',
          },
          age: {
            type: 'number',
            title: 'Age',
          },
        },
        required: ['name'],
      };

      useSchemaGraphStore.getState().setSchemaFromJson(inputSchema);

      const graph = useSchemaGraphStore.getState().graph;
      expect(graph.nodes.root.title).toBe('Person');
      expect(graph.nodes.root.children).toHaveLength(2);

      const nameNode = Object.values(graph.nodes).find(node => node.key === 'name');
      const ageNode = Object.values(graph.nodes).find(node => node.key === 'age');

      expect(nameNode).toBeDefined();
      expect(nameNode!.type).toBe('string');
      expect(nameNode!.required).toBe(true);

      expect(ageNode).toBeDefined();
      expect(ageNode!.type).toBe('number');
    });
  });

  describe('compileToJsonSchema', () => {
    it('should compile graph to JSON schema', () => {
      const stringData: Omit<FieldNode, 'id'> = {
        key: 'name',
        type: 'string',
        title: 'Name',
        required: true,
      };

      const numberData: Omit<FieldNode, 'id'> = {
        key: 'age',
        type: 'number',
        title: 'Age',
        minimum: 0,
      };

      useSchemaGraphStore.getState().addNode(stringData);
      useSchemaGraphStore.getState().addNode(numberData);

      const schema = useSchemaGraphStore.getState().compileToJsonSchema();

      expect(schema.type).toBe('object');
      expect(schema.properties).toBeDefined();
      expect(schema.properties!.name).toEqual({
        type: 'string',
        title: 'Name',
      });
      expect(schema.properties!.age).toEqual({
        type: 'number',
        title: 'Age',
        minimum: 0,
      });
      expect(schema.required).toEqual(['name']);
    });
  });

  describe('validateGraph', () => {
    it('should validate the current graph', () => {
      const nodeData: Omit<FieldNode, 'id'> = {
        key: 'test',
        type: 'string',
        title: 'Test',
      };

      useSchemaGraphStore.getState().addNode(nodeData);
      const validation = useSchemaGraphStore.getState().validateGraph();

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });
  });
});
